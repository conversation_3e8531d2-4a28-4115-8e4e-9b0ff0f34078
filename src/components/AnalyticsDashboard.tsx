
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, TrendingUp, Users, Clock, Target, Download, Filter } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AnalyticsDashboardProps {
  onBack: () => void;
}

const AnalyticsDashboard = ({ onBack }: AnalyticsDashboardProps) => {
  const stats = [
    { title: "Total Learners", value: "2,847", change: "+12%", icon: Users, color: "blue" },
    { title: "Completion Rate", value: "84%", change: "+5%", icon: Target, color: "green" },
    { title: "Avg. Time Spent", value: "24m", change: "-2m", icon: Clock, color: "orange" },
    { title: "Engagement Score", value: "8.9/10", change: "+0.3", icon: TrendingUp, color: "purple" }
  ];

  const coursePerformance = [
    { name: "Introduction to Digital Marketing", learners: 534, completion: 89, engagement: 9.2, rating: 4.8 },
    { name: "Advanced JavaScript Concepts", learners: 423, completion: 76, engagement: 8.7, rating: 4.6 },
    { name: "Project Management Essentials", learners: 678, completion: 94, engagement: 9.1, rating: 4.9 },
    { name: "Data Analytics Fundamentals", learners: 312, completion: 82, engagement: 8.4, rating: 4.5 }
  ];

  const learnerProgress = [
    { name: "Sarah Johnson", course: "Digital Marketing", progress: 85, timeSpent: "12h 30m", lastActive: "2 hours ago" },
    { name: "Mike Chen", course: "JavaScript Concepts", progress: 92, timeSpent: "18h 45m", lastActive: "1 day ago" },
    { name: "Emily Rodriguez", course: "Project Management", progress: 67, timeSpent: "8h 15m", lastActive: "3 hours ago" },
    { name: "David Kim", course: "Data Analytics", progress: 78, timeSpent: "14h 20m", lastActive: "5 hours ago" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Learning Analytics</h1>
                <p className="text-sm text-gray-600">Track learner progress and insights</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                    <p className={`text-sm mt-1 ${
                      stat.change.startsWith('+') ? 'text-green-600' : 
                      stat.change.startsWith('-') ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`w-12 h-12 bg-${stat.color}-100 rounded-lg flex items-center justify-center`}>
                    <stat.icon className={`w-6 h-6 text-${stat.color}-600`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Analytics */}
        <Tabs defaultValue="courses" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 lg:w-96">
            <TabsTrigger value="courses">Course Performance</TabsTrigger>
            <TabsTrigger value="learners">Learner Progress</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
          </TabsList>

          <TabsContent value="courses" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Course Performance Overview</CardTitle>
                <CardDescription>Track how your courses are performing with learners</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {coursePerformance.map((course, index) => (
                    <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-gray-900">{course.name}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>{course.learners} learners</span>
                          <Badge variant="outline">★ {course.rating}</Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-600">Completion Rate</span>
                            <span className="font-medium">{course.completion}%</span>
                          </div>
                          <Progress value={course.completion} className="h-2" />
                        </div>
                        
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-600">Engagement Score</span>
                            <span className="font-medium">{course.engagement}/10</span>
                          </div>
                          <Progress value={course.engagement * 10} className="h-2" />
                        </div>
                        
                        <div className="flex items-center justify-center">
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="learners" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Individual Learner Progress</CardTitle>
                <CardDescription>Monitor how learners are progressing through courses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {learnerProgress.map((learner, index) => (
                    <div key={index} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-gray-900">{learner.name}</h4>
                          <p className="text-sm text-gray-600">{learner.course}</p>
                        </div>
                        <div className="text-right text-sm text-gray-600">
                          <p>Time spent: {learner.timeSpent}</p>
                          <p>Last active: {learner.lastActive}</p>
                        </div>
                      </div>
                      
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-gray-600">Progress</span>
                          <span className="font-medium">{learner.progress}%</span>
                        </div>
                        <Progress value={learner.progress} className="h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="engagement" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Engagement Trends</CardTitle>
                  <CardDescription>Weekly engagement metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 bg-gradient-to-br from-blue-50 to-green-50 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <TrendingUp className="w-16 h-16 text-blue-500 mx-auto mb-4" />
                      <p className="text-gray-600">Engagement chart visualization</p>
                      <p className="text-sm text-gray-500">Interactive charts would be rendered here</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Popular Content Types</CardTitle>
                  <CardDescription>What engages learners most</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Interactive Videos</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={92} className="w-20 h-2" />
                        <span className="text-sm text-gray-600">92%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Hands-on Exercises</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={87} className="w-20 h-2" />
                        <span className="text-sm text-gray-600">87%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Quizzes & Assessments</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={76} className="w-20 h-2" />
                        <span className="text-sm text-gray-600">76%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Text Content</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={64} className="w-20 h-2" />
                        <span className="text-sm text-gray-600">64%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;


import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Plus, HelpCircle, CheckCircle, X, Save, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";

interface AssessmentBuilderProps {
  onBack: () => void;
}

const AssessmentBuilder = ({ onBack }: AssessmentBuilderProps) => {
  const [assessmentTitle, setAssessmentTitle] = useState("Knowledge Check");
  const [questions, setQuestions] = useState([
    {
      id: 1,
      type: "multiple-choice",
      question: "What is the primary purpose of e-learning authoring tools?",
      options: [
        { id: 1, text: "To create interactive learning content", correct: true },
        { id: 2, text: "To manage student databases", correct: false },
        { id: 3, text: "To design websites", correct: false },
        { id: 4, text: "To create presentations", correct: false }
      ],
      points: 10,
      timeLimit: 60
    }
  ]);

  const questionTypes = [
    { id: "multiple-choice", name: "Multiple Choice", icon: CheckCircle, description: "Single correct answer" },
    { id: "multiple-select", name: "Multiple Select", icon: CheckCircle, description: "Multiple correct answers" },
    { id: "true-false", name: "True/False", icon: HelpCircle, description: "Binary choice question" },
    { id: "short-answer", name: "Short Answer", icon: HelpCircle, description: "Text input response" },
    { id: "essay", name: "Essay", icon: HelpCircle, description: "Long-form response" },
    { id: "drag-drop", name: "Drag & Drop", icon: HelpCircle, description: "Interactive matching" }
  ];

  const addQuestion = (type: string) => {
    const newQuestion = {
      id: Date.now(),
      type,
      question: "",
      options: type === "multiple-choice" || type === "multiple-select" ? [
        { id: 1, text: "", correct: false },
        { id: 2, text: "", correct: false },
        { id: 3, text: "", correct: false },
        { id: 4, text: "", correct: false }
      ] : [],
      points: 10,
      timeLimit: 60
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (questionId: number, field: string, value: any) => {
    setQuestions(questions.map(q => 
      q.id === questionId ? { ...q, [field]: value } : q
    ));
  };

  const updateOption = (questionId: number, optionId: number, field: string, value: any) => {
    setQuestions(questions.map(q => 
      q.id === questionId ? {
        ...q,
        options: q.options.map(opt => 
          opt.id === optionId ? { ...opt, [field]: value } : opt
        )
      } : q
    ));
  };

  const removeQuestion = (questionId: number) => {
    setQuestions(questions.filter(q => q.id !== questionId));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Assessment Builder</h1>
                <p className="text-sm text-gray-600">Create interactive assessments</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              <Button size="sm" className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                <Save className="w-4 h-4 mr-2" />
                Save Assessment
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Question Types */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Question Types</CardTitle>
                <CardDescription>Click to add questions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {questionTypes.map((type) => (
                    <div
                      key={type.id}
                      className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      onClick={() => addQuestion(type.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <type.icon className="w-5 h-5 text-blue-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-sm">{type.name}</h4>
                          <p className="text-xs text-gray-500">{type.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Assessment Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Assessment Title</Label>
                  <Input 
                    id="title"
                    value={assessmentTitle}
                    onChange={(e) => setAssessmentTitle(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                  <Input 
                    id="timeLimit"
                    type="number"
                    placeholder="30"
                  />
                </div>
                <div>
                  <Label htmlFor="attempts">Max Attempts</Label>
                  <select className="w-full mt-1 p-2 border rounded-md">
                    <option>1</option>
                    <option>2</option>
                    <option>3</option>
                    <option>Unlimited</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="passingGrade">Passing Grade (%)</Label>
                  <Input 
                    id="passingGrade"
                    type="number"
                    placeholder="70"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Assessment Editor */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>{assessmentTitle}</CardTitle>
                  <CardDescription>
                    {questions.length} question{questions.length !== 1 ? 's' : ''}
                  </CardDescription>
                </div>
                <Button onClick={() => addQuestion('multiple-choice')}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Question
                </Button>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px] pr-4">
                  <div className="space-y-6">
                    {questions.map((question, index) => (
                      <Card key={question.id} className="border-l-4 border-l-blue-500">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Badge variant="outline" className="text-xs">
                                Question {index + 1}
                              </Badge>
                              <Badge variant="secondary" className="text-xs capitalize">
                                {question.type.replace('-', ' ')}
                              </Badge>
                              <span className="text-sm text-gray-600">
                                {question.points} points
                              </span>
                            </div>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => removeQuestion(question.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <Label>Question</Label>
                            <Textarea
                              value={question.question}
                              onChange={(e) => updateQuestion(question.id, 'question', e.target.value)}
                              placeholder="Enter your question here..."
                              rows={2}
                            />
                          </div>

                          {(question.type === 'multiple-choice' || question.type === 'multiple-select') && (
                            <div>
                              <Label>Answer Options</Label>
                              <div className="space-y-2 mt-2">
                                {question.options.map((option, optionIndex) => (
                                  <div key={option.id} className="flex items-center space-x-3">
                                    <input
                                      type={question.type === 'multiple-choice' ? 'radio' : 'checkbox'}
                                      name={`question-${question.id}`}
                                      checked={option.correct}
                                      onChange={(e) => updateOption(question.id, option.id, 'correct', e.target.checked)}
                                      className="mt-1"
                                    />
                                    <Input
                                      value={option.text}
                                      onChange={(e) => updateOption(question.id, option.id, 'text', e.target.value)}
                                      placeholder={`Option ${optionIndex + 1}`}
                                      className="flex-1"
                                    />
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          <div className="flex items-center space-x-4 pt-2 border-t">
                            <div className="flex items-center space-x-2">
                              <Label htmlFor={`points-${question.id}`} className="text-sm">Points:</Label>
                              <Input
                                id={`points-${question.id}`}
                                type="number"
                                value={question.points}
                                onChange={(e) => updateQuestion(question.id, 'points', parseInt(e.target.value))}
                                className="w-16"
                                min="1"
                              />
                            </div>
                            <div className="flex items-center space-x-2">
                              <Label htmlFor={`time-${question.id}`} className="text-sm">Time (sec):</Label>
                              <Input
                                id={`time-${question.id}`}
                                type="number"
                                value={question.timeLimit}
                                onChange={(e) => updateQuestion(question.id, 'timeLimit', parseInt(e.target.value))}
                                className="w-16"
                                min="10"
                              />
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}

                    {questions.length === 0 && (
                      <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
                        <HelpCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Questions Yet</h3>
                        <p className="text-gray-600 mb-4">Start building your assessment by adding questions</p>
                        <Button onClick={() => addQuestion('multiple-choice')}>
                          <Plus className="w-4 h-4 mr-2" />
                          Add Your First Question
                        </Button>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentBuilder;

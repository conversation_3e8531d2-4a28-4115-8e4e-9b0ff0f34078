
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Users, MessageCircle, Share, UserPlus, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";

const CollaborationPanel = () => {
  const [activeTab, setActiveTab] = useState("team");

  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Lead Instructional Designer",
      avatar: "/placeholder.svg",
      status: "online",
      lastActive: "now",
      permissions: ["edit", "publish", "manage"]
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Content Creator",
      avatar: "/placeholder.svg",
      status: "away",
      lastActive: "2 hours ago",
      permissions: ["edit", "comment"]
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "Subject Matter Expert",
      avatar: "/placeholder.svg",
      status: "offline",
      lastActive: "1 day ago",
      permissions: ["comment", "review"]
    }
  ];

  const comments = [
    {
      id: 1,
      author: "Sarah Johnson",
      content: "The introduction module needs more interactive elements. Consider adding a video overview.",
      timestamp: "2 hours ago",
      module: "Introduction to Digital Marketing",
      replies: 2,
      resolved: false
    },
    {
      id: 2,
      author: "Mike Chen",
      content: "Assessment questions in module 3 might be too challenging for beginners. Should we adjust the difficulty?",
      timestamp: "4 hours ago",
      module: "Core Concepts Overview",
      replies: 1,
      resolved: true
    },
    {
      id: 3,
      author: "Emily Rodriguez",
      content: "Great work on the visual design! The infographics really help explain complex concepts.",
      timestamp: "1 day ago",
      module: "Interactive Exercise",
      replies: 0,
      resolved: false
    }
  ];

  const recentActivity = [
    {
      id: 1,
      user: "Sarah Johnson",
      action: "edited",
      target: "Introduction module",
      timestamp: "30 minutes ago",
      type: "edit"
    },
    {
      id: 2,
      user: "Mike Chen",
      action: "added comment to",
      target: "Assessment Builder",
      timestamp: "1 hour ago",
      type: "comment"
    },
    {
      id: 3,
      user: "Emily Rodriguez",
      action: "reviewed",
      target: "Course Structure",
      timestamp: "2 hours ago",
      type: "review"
    },
    {
      id: 4,
      user: "Sarah Johnson",
      action: "published",
      target: "Digital Marketing Course v1.2",
      timestamp: "3 hours ago",
      type: "publish"
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Team Collaboration</h2>
          <p className="text-gray-600">Manage your course development team</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-600 to-green-600">
          <UserPlus className="w-4 h-4 mr-2" />
          Invite Member
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="team">Team Members</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="team" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {teamMembers.map((member) => (
              <Card key={member.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="relative">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                        member.status === 'online' ? 'bg-green-500' :
                        member.status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'
                      }`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 truncate">{member.name}</h3>
                      <p className="text-sm text-gray-600 truncate">{member.role}</p>
                      <p className="text-xs text-gray-500">Last active: {member.lastActive}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-1">
                      {member.permissions.map((permission) => (
                        <Badge key={permission} variant="secondary" className="text-xs">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <MessageCircle className="w-3 h-3 mr-1" />
                        Message
                      </Button>
                      <Button size="sm" variant="outline">
                        <Share className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="comments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Comments & Feedback</CardTitle>
              <CardDescription>Team discussions and review comments</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {comments.map((comment) => (
                    <div key={comment.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback>{comment.author.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-medium text-sm">{comment.author}</h4>
                            <p className="text-xs text-gray-500">{comment.timestamp}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {comment.resolved ? (
                            <Badge variant="outline" className="bg-green-50 text-green-700">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Resolved
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-orange-50 text-orange-700">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Open
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-700 mb-2">{comment.content}</p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>Module: {comment.module}</span>
                        <span>{comment.replies} replies</span>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Track team actions and changes</CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        activity.type === 'edit' ? 'bg-blue-100' :
                        activity.type === 'comment' ? 'bg-green-100' :
                        activity.type === 'review' ? 'bg-purple-100' :
                        'bg-orange-100'
                      }`}>
                        {activity.type === 'edit' && <Clock className="w-4 h-4 text-blue-600" />}
                        {activity.type === 'comment' && <MessageCircle className="w-4 h-4 text-green-600" />}
                        {activity.type === 'review' && <CheckCircle className="w-4 h-4 text-purple-600" />}
                        {activity.type === 'publish' && <Share className="w-4 h-4 text-orange-600" />}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm">
                          <span className="font-medium">{activity.user}</span>
                          <span className="text-gray-600"> {activity.action} </span>
                          <span className="font-medium">{activity.target}</span>
                        </p>
                        <p className="text-xs text-gray-500">{activity.timestamp}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Role Permissions</CardTitle>
                <CardDescription>Define what each role can do</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Lead Instructional Designer</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Create & Edit Content</span>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Publish Courses</span>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Manage Team</span>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">View Analytics</span>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-2">Content Creator</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Create & Edit Content</span>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Comment & Review</span>
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Publish Courses</span>
                        <X className="w-4 h-4 text-red-600" />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Invite New Member</CardTitle>
                <CardDescription>Add someone to your team</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <select id="role" className="w-full mt-1 p-2 border rounded-md">
                    <option>Content Creator</option>
                    <option>Subject Matter Expert</option>
                    <option>Reviewer</option>
                    <option>Lead Instructional Designer</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="message">Personal Message (Optional)</Label>
                  <Input id="message" placeholder="Join our course development team..." />
                </div>
                <Button className="w-full bg-gradient-to-r from-blue-600 to-green-600">
                  Send Invitation
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CollaborationPanel;

import { useDrag } from "react-dnd";
import { Button } from "@/components/ui/button";
import { Video, FileText, HelpCircle, Image, Mic, Code } from "lucide-react";

interface DraggableContentToolProps {
  type: 'video' | 'text' | 'quiz' | 'image' | 'audio' | 'interactive';
  label: string;
  description: string;
}

const DraggableContentTool = ({ type, label, description }: DraggableContentToolProps) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'content-tool',
    item: () => ({
      type: 'content-tool',
      contentType: type,
      label,
      description
    }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const getIcon = () => {
    switch (type) {
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'text':
        return <FileText className="w-5 h-5" />;
      case 'quiz':
        return <HelpCircle className="w-5 h-5" />;
      case 'image':
        return <Image className="w-5 h-5" />;
      case 'audio':
        return <Mic className="w-5 h-5" />;
      case 'interactive':
        return <Code className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const getColor = () => {
    switch (type) {
      case 'video':
        return 'text-red-600 bg-red-50 hover:bg-red-100 border-red-200';
      case 'text':
        return 'text-blue-600 bg-blue-50 hover:bg-blue-100 border-blue-200';
      case 'quiz':
        return 'text-purple-600 bg-purple-50 hover:bg-purple-100 border-purple-200';
      case 'image':
        return 'text-green-600 bg-green-50 hover:bg-green-100 border-green-200';
      case 'audio':
        return 'text-orange-600 bg-orange-50 hover:bg-orange-100 border-orange-200';
      case 'interactive':
        return 'text-indigo-600 bg-indigo-50 hover:bg-indigo-100 border-indigo-200';
      default:
        return 'text-gray-600 bg-gray-50 hover:bg-gray-100 border-gray-200';
    }
  };

  return (
    <Button
      ref={drag}
      variant="outline"
      className={`w-full justify-start p-4 h-auto flex-col items-start space-y-2 cursor-grab active:cursor-grabbing transition-all duration-200 ${getColor()} ${
        isDragging ? 'opacity-50 scale-95 shadow-lg' : ''
      }`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      <div className="flex items-center space-x-2 w-full">
        {getIcon()}
        <span className="font-medium">{label}</span>
      </div>
      <p className="text-xs text-left opacity-75">{description}</p>
    </Button>
  );
};

export default DraggableContentTool;

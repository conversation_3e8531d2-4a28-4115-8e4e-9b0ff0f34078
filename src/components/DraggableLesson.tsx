import { useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { GripVertical, Edit, Trash2, Video, FileText, HelpCircle, Image, Mic, Code } from "lucide-react";
import { Lesson, DragItem } from "@/types/course";

interface DraggableLessonProps {
  lesson: Lesson;
  moduleId: string;
  index: number;
  onMove: (dragIndex: number, hoverIndex: number, moduleId: string) => void;
  onClick: (lesson: Lesson) => void;
  onDelete?: (lessonId: string, moduleId: string) => void;
}

const DraggableLesson = ({ 
  lesson, 
  moduleId,
  index, 
  onMove, 
  onClick, 
  onDelete 
}: DraggableLessonProps) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ handlerId }, drop] = useDrop({
    accept: 'lesson',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      
      // Only allow reordering within the same module
      if (item.moduleId !== moduleId) {
        return;
      }

      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = clientOffset!.y - hoverBoundingRect.top;

      // Only perform the move when the mouse has crossed half of the items height
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      // Time to actually perform the action
      onMove(dragIndex, hoverIndex, moduleId);

      // Note: we're mutating the monitor item here!
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: 'lesson',
    item: () => {
      return { id: lesson.id, index, type: 'lesson', moduleId } as DragItem;
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0.4 : 1;
  
  // Connect drag and drop refs
  drag(drop(ref));

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="h-4 w-4" />;
      case 'text':
        return <FileText className="h-4 w-4" />;
      case 'quiz':
        return <HelpCircle className="h-4 w-4" />;
      case 'image':
        return <Image className="h-4 w-4" />;
      case 'audio':
        return <Mic className="h-4 w-4" />;
      case 'interactive':
        return <Code className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'text-red-600';
      case 'text':
        return 'text-blue-600';
      case 'quiz':
        return 'text-purple-600';
      case 'image':
        return 'text-green-600';
      case 'audio':
        return 'text-orange-600';
      case 'interactive':
        return 'text-indigo-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <Card 
      ref={ref}
      style={{ opacity }}
      data-handler-id={handlerId}
      className={`ml-6 hover:shadow-sm transition-all duration-200 cursor-pointer border-l-2 ${
        lesson.status === 'completed' ? 'border-l-green-400' : 
        lesson.status === 'in-progress' ? 'border-l-blue-400' : 
        'border-l-gray-300'
      } ${isDragging ? 'shadow-md scale-105' : ''}`}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="cursor-grab active:cursor-grabbing">
              <GripVertical className="h-3 w-3 text-gray-400" />
            </div>
            <div className={`${getTypeColor(lesson.type)}`}>
              {getTypeIcon(lesson.type)}
            </div>
            <div className="flex-1">
              <h5 className="font-medium text-sm text-gray-900">{lesson.title}</h5>
              <p className="text-xs text-gray-500 capitalize">{lesson.type} • {lesson.duration}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge 
              variant="outline" 
              className={`text-xs ${getStatusColor(lesson.status)}`}
            >
              {lesson.status}
            </Badge>
            
            <div className="flex items-center space-x-1">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  onClick(lesson);
                }}
                className="h-6 w-6 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              
              {onDelete && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(lesson.id, moduleId);
                  }}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DraggableLesson;

import { useDrop } from "react-dnd";
import { DragItem } from "@/types/course";

interface DropZoneProps {
  accept: 'module' | 'lesson';
  onDrop: (item: DragItem) => void;
  className?: string;
  children?: React.ReactNode;
  moduleId?: string; // For lesson drop zones
}

const DropZone = ({ accept, onDrop, className = "", children, moduleId }: DropZoneProps) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept,
    drop: (item: DragItem) => {
      // For lessons, only allow dropping within the same module or to empty zones
      if (accept === 'lesson' && item.moduleId && moduleId && item.moduleId !== moduleId) {
        return;
      }
      onDrop(item);
    },
    canDrop: (item: DragItem) => {
      // For lessons, only allow dropping within the same module
      if (accept === 'lesson' && item.moduleId && moduleId && item.moduleId !== moduleId) {
        return false;
      }
      return true;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const isActive = isOver && canDrop;
  const isInvalid = isOver && !canDrop;

  let backgroundColor = 'transparent';
  let borderColor = 'transparent';
  let borderStyle = 'dashed';

  if (isActive) {
    backgroundColor = accept === 'module' ? 'rgba(59, 130, 246, 0.1)' : 'rgba(16, 185, 129, 0.1)';
    borderColor = accept === 'module' ? '#3b82f6' : '#10b981';
  } else if (isInvalid) {
    backgroundColor = 'rgba(239, 68, 68, 0.1)';
    borderColor = '#ef4444';
  }

  return (
    <div
      ref={drop}
      className={`transition-all duration-200 ${className}`}
      style={{
        backgroundColor,
        borderColor,
        borderWidth: isActive || isInvalid ? '2px' : '1px',
        borderStyle: isActive || isInvalid ? borderStyle : 'solid',
        borderRadius: '8px',
        minHeight: isActive || isInvalid ? '60px' : 'auto',
      }}
    >
      {children}
      {isActive && (
        <div className="flex items-center justify-center p-4 text-sm font-medium text-gray-600">
          <span className={accept === 'module' ? 'text-blue-600' : 'text-green-600'}>
            Drop {accept} here
          </span>
        </div>
      )}
      {isInvalid && (
        <div className="flex items-center justify-center p-4 text-sm font-medium text-red-600">
          Cannot drop here
        </div>
      )}
    </div>
  );
};

export default DropZone;

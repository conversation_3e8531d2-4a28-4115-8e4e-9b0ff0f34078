
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Save, Eye, Upload, Type, Image, Video, Mic, Code, HelpCircle } from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface ModuleEditorProps {
  module: any;
  onBack: () => void;
  onSave: (module: any) => void;
}

const ModuleEditor = ({ module, onBack, onSave }: ModuleEditorProps) => {
  const [title, setTitle] = useState(module.title);
  const [content, setContent] = useState(module.content);
  const [duration, setDuration] = useState(module.duration);
  const [activeTab, setActiveTab] = useState("content");

  const handleSave = () => {
    onSave({
      ...module,
      title,
      content,
      duration,
      status: "completed"
    });
  };

  const editorTools = [
    { id: "text", name: "Text", icon: Type, description: "Add formatted text content" },
    { id: "image", name: "Image", icon: Image, description: "Insert images and graphics" },
    { id: "video", name: "Video", icon: Video, description: "Embed video content" },
    { id: "audio", name: "Audio", icon: Mic, description: "Add audio recordings" },
    { id: "code", name: "Code", icon: Code, description: "Insert code snippets" },
    { id: "quiz", name: "Quiz", icon: HelpCircle, description: "Add interactive quiz" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Module Editor</h1>
                <p className="text-sm text-gray-600">{module.type} Module</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              <Button onClick={handleSave} size="sm" className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                <Save className="w-4 h-4 mr-2" />
                Save Module
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Tools */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Content Tools</CardTitle>
                <CardDescription>Drag to add content elements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {editorTools.map((tool) => (
                    <div
                      key={tool.id}
                      className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                      draggable
                    >
                      <div className="flex items-center space-x-3">
                        <tool.icon className="w-5 h-5 text-gray-600" />
                        <div>
                          <h4 className="font-medium text-sm">{tool.name}</h4>
                          <p className="text-xs text-gray-500">{tool.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Module Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="duration">Duration</Label>
                  <Input 
                    id="duration"
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    placeholder="e.g., 15 min"
                  />
                </div>
                <div>
                  <Label htmlFor="difficulty">Difficulty</Label>
                  <select className="w-full mt-1 p-2 border rounded-md">
                    <option>Beginner</option>
                    <option>Intermediate</option>
                    <option>Advanced</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="prerequisites">Prerequisites</Label>
                  <Textarea 
                    id="prerequisites"
                    placeholder="List any prerequisites..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Editor */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <Input 
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      className="text-xl font-bold border-none p-0 focus-visible:ring-0"
                      placeholder="Module Title..."
                    />
                    <p className="text-sm text-gray-600 mt-1">Type: {module.type}</p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="content">Content</TabsTrigger>
                    <TabsTrigger value="media">Media</TabsTrigger>
                    <TabsTrigger value="interactive">Interactive</TabsTrigger>
                  </TabsList>

                  <TabsContent value="content" className="space-y-4">
                    <div className="min-h-[400px] border rounded-lg p-4 bg-white">
                      <div className="mb-4 flex items-center justify-between border-b pb-2">
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Type className="w-4 h-4" />
                          </Button>
                          <Separator orientation="vertical" className="h-6" />
                          <Button variant="ghost" size="sm">Bold</Button>
                          <Button variant="ghost" size="sm">Italic</Button>
                          <Button variant="ghost" size="sm">Link</Button>
                          <Separator orientation="vertical" className="h-6" />
                          <Button variant="ghost" size="sm">
                            <Upload className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <Textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        placeholder="Start writing your module content here... You can use markdown formatting."
                        className="min-h-[300px] border-none resize-none focus-visible:ring-0"
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="media" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-6 text-center">
                          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="font-medium mb-2">Upload Images</h3>
                          <p className="text-sm text-gray-600">Drag and drop or click to upload</p>
                        </CardContent>
                      </Card>
                      
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-6 text-center">
                          <Video className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="font-medium mb-2">Add Video</h3>
                          <p className="text-sm text-gray-600">Upload or embed video content</p>
                        </CardContent>
                      </Card>
                      
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-6 text-center">
                          <Mic className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="font-medium mb-2">Audio Content</h3>
                          <p className="text-sm text-gray-600">Record or upload audio</p>
                        </CardContent>
                      </Card>
                      
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-6 text-center">
                          <Code className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="font-medium mb-2">Code Snippets</h3>
                          <p className="text-sm text-gray-600">Add syntax-highlighted code</p>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="interactive" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-6 text-center">
                          <HelpCircle className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                          <h3 className="font-medium mb-2">Quick Quiz</h3>
                          <p className="text-sm text-gray-600">Add interactive questions</p>
                        </CardContent>
                      </Card>
                      
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-6 text-center">
                          <Code className="w-12 h-12 text-green-500 mx-auto mb-4" />
                          <h3 className="font-medium mb-2">Interactive Demo</h3>
                          <p className="text-sm text-gray-600">Create hands-on exercises</p>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModuleEditor;

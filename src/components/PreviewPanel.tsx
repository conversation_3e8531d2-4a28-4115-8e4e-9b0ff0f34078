
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Play, Pause, SkipForward, SkipBack, Monitor, Smartphone, Tablet } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";

interface PreviewPanelProps {
  course: any;
  onBack: () => void;
}

const PreviewPanel = ({ course, onBack }: PreviewPanelProps) => {
  const [currentModule, setCurrentModule] = useState(0);
  const [deviceView, setDeviceView] = useState("desktop");
  const [isPlaying, setIsPlaying] = useState(false);

  const progress = ((currentModule + 1) / course.modules.length) * 100;

  const deviceStyles = {
    desktop: "w-full max-w-6xl",
    tablet: "w-full max-w-2xl",
    mobile: "w-full max-w-sm"
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Course Preview</h1>
                <p className="text-sm text-gray-600">{course.title}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Device Toggle */}
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <Button
                  variant={deviceView === "desktop" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setDeviceView("desktop")}
                  className="p-2"
                >
                  <Monitor className="w-4 h-4" />
                </Button>
                <Button
                  variant={deviceView === "tablet" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setDeviceView("tablet")}
                  className="p-2"
                >
                  <Tablet className="w-4 h-4" />
                </Button>
                <Button
                  variant={deviceView === "mobile" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setDeviceView("mobile")}
                  className="p-2"
                >
                  <Smartphone className="w-4 h-4" />
                </Button>
              </div>
              
              {/* Playback Controls */}
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <Button
                  variant="ghost" 
                  size="sm"
                  onClick={() => setCurrentModule(Math.max(0, currentModule - 1))}
                  disabled={currentModule === 0}
                  className="p-2"
                >
                  <SkipBack className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost" 
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                  className="p-2"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                <Button
                  variant="ghost" 
                  size="sm"
                  onClick={() => setCurrentModule(Math.min(course.modules.length - 1, currentModule + 1))}
                  disabled={currentModule === course.modules.length - 1}
                  className="p-2"
                >
                  <SkipForward className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Course Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{course.title}</CardTitle>
                <CardDescription>{course.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Progress</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    Module {currentModule + 1} of {course.modules.length}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-base">Course Modules</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {course.modules.map((module, index) => (
                      <div
                        key={module.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors ${
                          index === currentModule 
                            ? 'bg-blue-100 border-blue-500 border' 
                            : 'hover:bg-gray-50 border border-transparent'
                        }`}
                        onClick={() => setCurrentModule(index)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                            index === currentModule 
                              ? 'bg-blue-600 text-white' 
                              : index < currentModule 
                                ? 'bg-green-600 text-white' 
                                : 'bg-gray-200 text-gray-600'
                          }`}>
                            {index + 1}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate">{module.title}</h4>
                            <p className="text-xs text-gray-500 capitalize">{module.type} • {module.duration}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Main Preview Area */}
          <div className="lg:col-span-3">
            <div className="flex justify-center">
              <div className={`${deviceStyles[deviceView]} transition-all duration-300`}>
                <Card className="min-h-[600px]">
                  <CardHeader className="border-b">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">
                          {course.modules[currentModule]?.title}
                        </CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline" className="capitalize">
                            {course.modules[currentModule]?.type}
                          </Badge>
                          <span className="text-sm text-gray-600">
                            {course.modules[currentModule]?.duration}
                          </span>
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {currentModule + 1} / {course.modules.length}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-8">
                    <div className="space-y-6">
                      {/* Simulated Content */}
                      <div className="prose max-w-none">
                        <h2 className="text-2xl font-bold text-gray-900 mb-4">
                          {course.modules[currentModule]?.title}
                        </h2>
                        
                        {course.modules[currentModule]?.type === "video" && (
                          <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center mb-6">
                            <div className="text-center text-white">
                              <Play className="w-16 h-16 mx-auto mb-4 opacity-60" />
                              <p className="text-lg">Video Content</p>
                              <p className="text-sm opacity-60">Click to play</p>
                            </div>
                          </div>
                        )}
                        
                        <div className="text-gray-700 leading-relaxed">
                          <p className="mb-4">
                            Welcome to this comprehensive learning module. This interactive content is designed 
                            to provide you with practical knowledge and hands-on experience.
                          </p>
                          
                          <p className="mb-4">
                            {course.modules[currentModule]?.content || 
                            "In this section, you'll explore key concepts through engaging multimedia content, " +
                            "interactive exercises, and real-world examples that reinforce your understanding."}
                          </p>
                          
                          <div className="bg-blue-50 border-l-4 border-blue-500 p-4 my-6">
                            <h4 className="font-semibold text-blue-900 mb-2">Key Takeaway</h4>
                            <p className="text-blue-800">
                              This module builds upon previous concepts and prepares you for advanced topics 
                              in upcoming sections.
                            </p>
                          </div>
                          
                          {course.modules[currentModule]?.type === "quiz" && (
                            <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 mt-6">
                              <h4 className="font-semibold text-orange-900 mb-4">Quick Assessment</h4>
                              <div className="space-y-4">
                                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                                  <p className="font-medium mb-2">What is the primary benefit of interactive learning?</p>
                                  <div className="space-y-2 text-sm">
                                    <label className="flex items-center space-x-2">
                                      <input type="radio" name="q1" />
                                      <span>Increased engagement and retention</span>
                                    </label>
                                    <label className="flex items-center space-x-2">
                                      <input type="radio" name="q1" />
                                      <span>Faster completion times</span>
                                    </label>
                                    <label className="flex items-center space-x-2">
                                      <input type="radio" name="q1" />
                                      <span>Lower development costs</span>
                                    </label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      {/* Navigation */}
                      <div className="flex justify-between pt-6 border-t">
                        <Button 
                          variant="outline"
                          onClick={() => setCurrentModule(Math.max(0, currentModule - 1))}
                          disabled={currentModule === 0}
                        >
                          Previous Module
                        </Button>
                        <Button 
                          onClick={() => setCurrentModule(Math.min(course.modules.length - 1, currentModule + 1))}
                          disabled={currentModule === course.modules.length - 1}
                          className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
                        >
                          Next Module
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PreviewPanel;

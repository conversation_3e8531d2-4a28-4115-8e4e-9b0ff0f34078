export interface TemplateModule {
  id: string;
  title: string;
  description: string;
  type: 'lesson' | 'quiz' | 'video' | 'interactive' | 'assignment';
  duration: string;
  content?: string;
  questions?: Array<{
    id: string;
    question: string;
    type: 'multiple-choice' | 'true-false' | 'short-answer';
    options?: string[];
    correctAnswer?: string;
  }>;
}

export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  duration: string;
  thumbnail: string;
  tags: string[];
  modules: TemplateModule[];
  learningObjectives: string[];
  targetAudience: string;
}

export const templates: Template[] = [
  {
    id: 'employee-onboarding',
    name: 'Employee Onboarding Program',
    description: 'Comprehensive onboarding course for new employees covering company culture, policies, and essential skills.',
    category: 'Corporate Training',
    difficulty: 'Beginner',
    duration: '4 hours',
    thumbnail: '🏢',
    tags: ['HR', 'Onboarding', 'Corporate', 'New Hire'],
    learningObjectives: [
      'Understand company mission, vision, and values',
      'Learn key policies and procedures',
      'Complete mandatory compliance training',
      'Set up essential tools and accounts'
    ],
    targetAudience: 'New employees across all departments',
    modules: [
      {
        id: 'welcome',
        title: 'Welcome to the Company',
        description: 'Introduction to company culture, history, and values',
        type: 'lesson',
        duration: '30 min',
        content: 'Welcome to our organization! In this module, you\'ll learn about our company\'s rich history, core values, and the culture that makes us unique...'
      },
      {
        id: 'policies',
        title: 'Company Policies & Procedures',
        description: 'Essential policies every employee should know',
        type: 'lesson',
        duration: '45 min',
        content: 'Understanding our policies is crucial for a successful career here. This section covers HR policies, code of conduct, and workplace guidelines...'
      },
      {
        id: 'compliance-quiz',
        title: 'Compliance Knowledge Check',
        description: 'Test your understanding of company policies',
        type: 'quiz',
        duration: '15 min',
        questions: [
          {
            id: 'q1',
            question: 'What is the company\'s policy on remote work?',
            type: 'multiple-choice',
            options: ['Fully remote', 'Hybrid - 3 days in office', 'Fully in-office', 'Flexible arrangement'],
            correctAnswer: 'Hybrid - 3 days in office'
          },
          {
            id: 'q2',
            question: 'Harassment should be reported immediately to HR.',
            type: 'true-false',
            correctAnswer: 'true'
          }
        ]
      },
      {
        id: 'tools-setup',
        title: 'Essential Tools Setup',
        description: 'Setting up your workspace and essential software',
        type: 'interactive',
        duration: '60 min',
        content: 'Follow these step-by-step instructions to set up your development environment, communication tools, and project management software...'
      }
    ]
  },
  {
    id: 'digital-marketing',
    name: 'Digital Marketing Fundamentals',
    description: 'Learn the basics of digital marketing including SEO, social media, and content marketing strategies.',
    category: 'Marketing',
    difficulty: 'Intermediate',
    duration: '6 hours',
    thumbnail: '📱',
    tags: ['Marketing', 'SEO', 'Social Media', 'Content'],
    learningObjectives: [
      'Master SEO fundamentals and keyword research',
      'Create effective social media campaigns',
      'Develop content marketing strategies',
      'Analyze marketing metrics and ROI'
    ],
    targetAudience: 'Marketing professionals and business owners',
    modules: [
      {
        id: 'seo-basics',
        title: 'SEO Fundamentals',
        description: 'Understanding search engine optimization principles',
        type: 'lesson',
        duration: '90 min',
        content: 'Search Engine Optimization (SEO) is the practice of increasing the quantity and quality of traffic to your website through organic search engine results...'
      },
      {
        id: 'social-media',
        title: 'Social Media Marketing',
        description: 'Building effective social media presence',
        type: 'video',
        duration: '75 min',
        content: 'Video content covering platform-specific strategies for Facebook, Instagram, LinkedIn, and Twitter...'
      },
      {
        id: 'content-strategy',
        title: 'Content Marketing Strategy',
        description: 'Creating compelling content that converts',
        type: 'lesson',
        duration: '60 min',
        content: 'Content marketing is about creating valuable, relevant content to attract and retain a clearly defined audience...'
      },
      {
        id: 'analytics-quiz',
        title: 'Marketing Analytics Assessment',
        description: 'Test your knowledge of marketing metrics',
        type: 'quiz',
        duration: '20 min',
        questions: [
          {
            id: 'q1',
            question: 'What does CTR stand for in digital marketing?',
            type: 'multiple-choice',
            options: ['Click Through Rate', 'Cost To Revenue', 'Customer Target Reach', 'Content Time Ratio'],
            correctAnswer: 'Click Through Rate'
          }
        ]
      }
    ]
  },
  {
    id: 'python-programming',
    name: 'Python Programming for Beginners',
    description: 'Complete introduction to Python programming with hands-on exercises and real-world projects.',
    category: 'Programming',
    difficulty: 'Beginner',
    duration: '8 hours',
    thumbnail: '🐍',
    tags: ['Python', 'Programming', 'Coding', 'Beginner'],
    learningObjectives: [
      'Understand Python syntax and basic concepts',
      'Work with data types, variables, and functions',
      'Build simple applications and scripts',
      'Debug and test Python code effectively'
    ],
    targetAudience: 'Complete programming beginners and career changers',
    modules: [
      {
        id: 'python-intro',
        title: 'Introduction to Python',
        description: 'Getting started with Python programming',
        type: 'lesson',
        duration: '45 min',
        content: 'Python is a high-level, interpreted programming language known for its simplicity and readability...'
      },
      {
        id: 'variables-data',
        title: 'Variables and Data Types',
        description: 'Understanding Python data structures',
        type: 'interactive',
        duration: '90 min',
        content: 'Interactive coding exercises covering strings, numbers, lists, dictionaries, and more...'
      },
      {
        id: 'functions',
        title: 'Functions and Control Flow',
        description: 'Writing reusable code with functions',
        type: 'lesson',
        duration: '75 min',
        content: 'Functions are reusable blocks of code that perform specific tasks. Learn how to define, call, and optimize functions...'
      },
      {
        id: 'final-project',
        title: 'Build Your First Python App',
        description: 'Create a simple calculator application',
        type: 'assignment',
        duration: '120 min',
        content: 'Apply everything you\'ve learned to build a functional calculator with error handling and user interface...'
      }
    ]
  },
  {
    id: 'project-management',
    name: 'Agile Project Management',
    description: 'Master agile methodologies, scrum framework, and project management best practices.',
    category: 'Management',
    difficulty: 'Intermediate',
    duration: '5 hours',
    thumbnail: '📊',
    tags: ['Agile', 'Scrum', 'Project Management', 'Leadership'],
    learningObjectives: [
      'Understand agile principles and methodologies',
      'Implement scrum framework effectively',
      'Manage project timelines and resources',
      'Lead cross-functional teams successfully'
    ],
    targetAudience: 'Project managers, team leads, and aspiring managers',
    modules: [
      {
        id: 'agile-intro',
        title: 'Agile Methodology Overview',
        description: 'Understanding agile principles and values',
        type: 'lesson',
        duration: '60 min',
        content: 'Agile is a project management methodology that emphasizes flexibility, collaboration, and customer satisfaction...'
      },
      {
        id: 'scrum-framework',
        title: 'Scrum Framework Deep Dive',
        description: 'Roles, events, and artifacts in Scrum',
        type: 'video',
        duration: '90 min',
        content: 'Comprehensive video training on Scrum Master, Product Owner, and Development Team roles...'
      },
      {
        id: 'tools-techniques',
        title: 'Project Management Tools',
        description: 'Using digital tools for agile project management',
        type: 'interactive',
        duration: '45 min',
        content: 'Hands-on experience with popular tools like Jira, Trello, and Azure DevOps...'
      }
    ]
  },
  {
    id: 'customer-service',
    name: 'Excellence in Customer Service',
    description: 'Develop exceptional customer service skills and learn to handle challenging situations professionally.',
    category: 'Customer Service',
    difficulty: 'Beginner',
    duration: '3 hours',
    thumbnail: '🤝',
    tags: ['Customer Service', 'Communication', 'Problem Solving', 'Soft Skills'],
    learningObjectives: [
      'Master effective communication techniques',
      'Handle difficult customers with confidence',
      'Resolve conflicts and complaints professionally',
      'Build long-term customer relationships'
    ],
    targetAudience: 'Customer service representatives and support staff',
    modules: [
      {
        id: 'communication-skills',
        title: 'Effective Communication',
        description: 'Building rapport and active listening skills',
        type: 'lesson',
        duration: '45 min',
        content: 'Great customer service starts with excellent communication. Learn active listening, empathy, and clear communication techniques...'
      },
      {
        id: 'difficult-situations',
        title: 'Handling Difficult Customers',
        description: 'De-escalation and problem-solving strategies',
        type: 'video',
        duration: '60 min',
        content: 'Role-playing scenarios and proven techniques for managing challenging customer interactions...'
      },
      {
        id: 'service-scenarios',
        title: 'Customer Service Scenarios',
        description: 'Practice with real-world situations',
        type: 'quiz',
        duration: '30 min',
        questions: [
          {
            id: 'q1',
            question: 'A customer is angry about a delayed order. What should you do first?',
            type: 'multiple-choice',
            options: ['Apologize and listen', 'Explain company policy', 'Transfer to supervisor', 'Offer a discount'],
            correctAnswer: 'Apologize and listen'
          }
        ]
      }
    ]
  },
  {
    id: 'data-analysis',
    name: 'Data Analysis with Excel',
    description: 'Learn advanced Excel techniques for data analysis, visualization, and reporting.',
    category: 'Data Analysis',
    difficulty: 'Advanced',
    duration: '7 hours',
    thumbnail: '📈',
    tags: ['Excel', 'Data Analysis', 'Visualization', 'Reporting'],
    learningObjectives: [
      'Master advanced Excel formulas and functions',
      'Create dynamic charts and dashboards',
      'Perform statistical analysis and modeling',
      'Automate reports with macros and VBA'
    ],
    targetAudience: 'Business analysts, data professionals, and advanced Excel users',
    modules: [
      {
        id: 'advanced-formulas',
        title: 'Advanced Excel Formulas',
        description: 'VLOOKUP, INDEX/MATCH, and array formulas',
        type: 'interactive',
        duration: '120 min',
        content: 'Hands-on practice with complex formulas and functions for data manipulation and analysis...'
      },
      {
        id: 'data-visualization',
        title: 'Charts and Dashboards',
        description: 'Creating compelling data visualizations',
        type: 'lesson',
        duration: '90 min',
        content: 'Learn to create professional charts, pivot tables, and interactive dashboards that tell a story with data...'
      },
      {
        id: 'statistical-analysis',
        title: 'Statistical Analysis in Excel',
        description: 'Descriptive and inferential statistics',
        type: 'lesson',
        duration: '75 min',
        content: 'Perform statistical analysis using Excel\'s built-in tools and add-ins for regression, correlation, and hypothesis testing...'
      }
    ]
  }
];

export const getTemplateById = (id: string): Template | undefined => {
  return templates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category: string): Template[] => {
  return templates.filter(template => template.category === category);
};

export const getTemplatesByDifficulty = (difficulty: string): Template[] => {
  return templates.filter(template => template.difficulty === difficulty);
};

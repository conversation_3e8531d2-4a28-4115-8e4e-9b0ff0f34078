import { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  ArrowLeft, 
  Clock, 
  Users, 
  BookOpen, 
  Play, 
  FileText, 
  Video, 
  HelpCircle, 
  Code, 
  Assignment,
  CheckCircle,
  Target,
  Star
} from "lucide-react";
import { getTemplateById, TemplateModule } from "@/data/templates";
import { toast } from "@/components/ui/sonner";

const TemplateDetail = () => {
  const { templateId } = useParams<{ templateId: string }>();
  const navigate = useNavigate();
  const [selectedModule, setSelectedModule] = useState<TemplateModule | null>(null);
  
  const template = templateId ? getTemplateById(templateId) : null;

  if (!template) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Template not found</h2>
          <Button onClick={() => navigate("/templates")}>
            Back to Templates
          </Button>
        </div>
      </div>
    );
  }

  const getModuleIcon = (type: string) => {
    switch (type) {
      case 'lesson': return FileText;
      case 'video': return Video;
      case 'quiz': return HelpCircle;
      case 'interactive': return Code;
      case 'assignment': return Assignment;
      default: return BookOpen;
    }
  };

  const getModuleColor = (type: string) => {
    switch (type) {
      case 'lesson': return 'text-blue-600';
      case 'video': return 'text-red-600';
      case 'quiz': return 'text-orange-600';
      case 'interactive': return 'text-purple-600';
      case 'assignment': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleUseTemplate = () => {
    toast.success(`Starting with "${template.name}" template!`, {
      description: "You'll be redirected to the course builder with this template."
    });
    // Navigate to the main page with template selection
    // In a real app, this would pass the template data to the course builder
    setTimeout(() => {
      navigate("/", { state: { selectedTemplate: template } });
    }, 1000);
  };

  const handleSaveToFavorites = () => {
    toast.success(`"${template.name}" saved to favorites!`, {
      description: "You can find it in your favorites section."
    });
  };

  const handlePreviewDemo = () => {
    toast.info("Demo preview coming soon!", {
      description: "This feature will show an interactive preview of the course."
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={() => navigate("/templates")} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Template Preview</h1>
                <p className="text-sm text-gray-600">Review template details and content</p>
              </div>
            </div>
            <Button 
              className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
              onClick={handleUseTemplate}
            >
              Use This Template
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Template Header */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between mb-4">
                  <div className="text-6xl">{template.thumbnail}</div>
                  <Badge className={getDifficultyColor(template.difficulty)}>
                    {template.difficulty}
                  </Badge>
                </div>
                <CardTitle className="text-2xl">{template.name}</CardTitle>
                <CardDescription className="text-base">{template.description}</CardDescription>
                
                {/* Template Stats */}
                <div className="flex flex-wrap gap-4 mt-4 pt-4 border-t">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>{template.duration}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <BookOpen className="w-4 h-4" />
                    <span>{template.modules.length} modules</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Users className="w-4 h-4" />
                    <span>{template.targetAudience}</span>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mt-4">
                  <Badge variant="outline">{template.category}</Badge>
                  {template.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardHeader>
            </Card>

            {/* Content Tabs */}
            <Tabs defaultValue="modules" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="modules">Course Modules</TabsTrigger>
                <TabsTrigger value="objectives">Learning Objectives</TabsTrigger>
                <TabsTrigger value="preview">Content Preview</TabsTrigger>
              </TabsList>

              <TabsContent value="modules" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Course Structure</CardTitle>
                    <CardDescription>
                      This template includes {template.modules.length} modules covering all essential topics
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {template.modules.map((module, index) => {
                        const IconComponent = getModuleIcon(module.type);
                        return (
                          <Card 
                            key={module.id} 
                            className="hover:shadow-md transition-shadow cursor-pointer"
                            onClick={() => setSelectedModule(module)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                  <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                                    <IconComponent className={`w-5 h-5 ${getModuleColor(module.type)}`} />
                                  </div>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium text-gray-900 truncate">
                                      {index + 1}. {module.title}
                                    </h4>
                                    <span className="text-sm text-gray-500">{module.duration}</span>
                                  </div>
                                  <p className="text-sm text-gray-600 mt-1">{module.description}</p>
                                  <div className="flex items-center mt-2">
                                    <Badge variant="outline" className="text-xs capitalize">
                                      {module.type}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="objectives" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Target className="w-5 h-5" />
                      <span>Learning Objectives</span>
                    </CardTitle>
                    <CardDescription>
                      What learners will achieve after completing this course
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {template.learningObjectives.map((objective, index) => (
                        <div key={index} className="flex items-start space-x-3">
                          <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <p className="text-gray-700">{objective}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                {selectedModule ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        {(() => {
                          const IconComponent = getModuleIcon(selectedModule.type);
                          return <IconComponent className={`w-5 h-5 ${getModuleColor(selectedModule.type)}`} />;
                        })()}
                        <span>{selectedModule.title}</span>
                      </CardTitle>
                      <CardDescription>{selectedModule.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between text-sm">
                          <Badge variant="outline" className="capitalize">{selectedModule.type}</Badge>
                          <span className="text-gray-600">{selectedModule.duration}</span>
                        </div>
                        
                        {selectedModule.content && (
                          <div>
                            <h4 className="font-medium mb-2">Content Preview:</h4>
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <p className="text-gray-700 text-sm leading-relaxed">
                                {selectedModule.content}
                              </p>
                            </div>
                          </div>
                        )}

                        {selectedModule.questions && selectedModule.questions.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-2">Sample Questions:</h4>
                            <div className="space-y-3">
                              {selectedModule.questions.slice(0, 2).map((question, index) => (
                                <div key={question.id} className="bg-gray-50 p-4 rounded-lg">
                                  <p className="font-medium text-sm mb-2">
                                    {index + 1}. {question.question}
                                  </p>
                                  {question.options && (
                                    <div className="space-y-1">
                                      {question.options.map((option, optIndex) => (
                                        <div key={optIndex} className="text-sm text-gray-600">
                                          {String.fromCharCode(65 + optIndex)}. {option}
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Module</h3>
                      <p className="text-gray-600">
                        Click on any module from the "Course Modules" tab to preview its content here.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Get Started</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
                  onClick={handleUseTemplate}
                >
                  Use This Template
                </Button>
                <Button variant="outline" className="w-full" onClick={handleSaveToFavorites}>
                  <Star className="w-4 h-4 mr-2" />
                  Save to Favorites
                </Button>
                <Button variant="outline" className="w-full" onClick={handlePreviewDemo}>
                  Preview Demo
                </Button>
              </CardContent>
            </Card>

            {/* Template Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Template Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-1">Category</h4>
                  <p className="text-sm text-gray-600">{template.category}</p>
                </div>
                <Separator />
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-1">Difficulty Level</h4>
                  <Badge className={getDifficultyColor(template.difficulty)}>
                    {template.difficulty}
                  </Badge>
                </div>
                <Separator />
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-1">Estimated Duration</h4>
                  <p className="text-sm text-gray-600">{template.duration}</p>
                </div>
                <Separator />
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-1">Target Audience</h4>
                  <p className="text-sm text-gray-600">{template.targetAudience}</p>
                </div>
              </CardContent>
            </Card>

            {/* Progress Indicator */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Course Progress</CardTitle>
                <CardDescription>Sample learner progress</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Overall Progress</span>
                    <span>65%</span>
                  </div>
                  <Progress value={65} className="h-2" />
                  <div className="text-xs text-gray-600">
                    3 of {template.modules.length} modules completed
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplateDetail;
